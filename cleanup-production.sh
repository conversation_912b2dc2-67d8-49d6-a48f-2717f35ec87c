#!/bin/bash

# TMS REST API - Production Cleanup Script
# This script forcefully removes all TMS production containers, networks, and images
# Use this script when deployment fails due to container conflicts

set -e

# Configuration
TARGET_SERVER="williamdu@***********"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if we're running locally or on the server
check_environment() {
    if [[ $(hostname) == *"***********"* ]] || [[ $(hostname -I 2>/dev/null | grep -q "***********") ]]; then
        echo "server"
    else
        echo "local"
    fi
}

# Function to execute commands locally or remotely
execute_cmd() {
    local cmd="$1"
    local env=$(check_environment)
    
    if [[ "$env" == "server" ]]; then
        eval "$cmd"
    else
        ssh "$TARGET_SERVER" "$cmd"
    fi
}

echo -e "${RED}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                TMS PRODUCTION CLEANUP SCRIPT                ║"
echo "║                                                              ║"
echo "║  ⚠️  WARNING: This will forcefully remove ALL TMS           ║"
echo "║      production containers, networks, and images!           ║"
echo "║                                                              ║"
echo "║  Data volumes will be preserved to prevent data loss.       ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"
echo ""

# Confirm cleanup
echo -e "${YELLOW}⚠️  You are about to forcefully clean up ALL TMS production resources${NC}"
echo -e "${YELLOW}   This will stop and remove all TMS containers and networks.${NC}"
echo ""
read -p "Continue with cleanup? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Cleanup cancelled.${NC}"
    exit 0
fi

echo ""
log "Starting comprehensive TMS production cleanup..."

# Step 1: Show current state
log "Current TMS containers:"
execute_cmd "docker ps -a --format 'table {{.Names}}\t{{.Status}}\t{{.Image}}' | grep -E '(NAMES|tms-.*-prod-container)' || echo 'No TMS containers found'"

echo ""
log "Current TMS networks:"
execute_cmd "docker network ls --format 'table {{.Name}}\t{{.Driver}}' | grep -E '(NAME|tms)' || echo 'No TMS networks found'"

echo ""

# Step 2: Stop all TMS containers
log "Stopping all TMS production containers..."
TMS_CONTAINERS=(
    "tms-api-prod-container"
    "tms-postgres-prod-container"
    "tms-minio-prod-container"
    "tms-reverse-proxy-prod-container"
)

for container in "${TMS_CONTAINERS[@]}"; do
    if execute_cmd "docker ps --format '{{.Names}}' | grep -q '^${container}$'"; then
        log "Stopping container: $container"
        execute_cmd "docker stop $container || true"
        success "Stopped: $container"
    else
        log "Container not running: $container"
    fi
done

# Step 3: Remove all TMS containers (including stopped ones)
log "Removing all TMS production containers..."
for container in "${TMS_CONTAINERS[@]}"; do
    if execute_cmd "docker ps -a --format '{{.Names}}' | grep -q '^${container}$'"; then
        log "Removing container: $container"
        execute_cmd "docker rm -f $container || true"
        success "Removed: $container"
    else
        log "Container not found: $container"
    fi
done

# Step 4: Remove TMS networks
log "Removing TMS networks..."
TMS_NETWORKS=("tms-internal" "tms-api_tms-internal")

for network in "${TMS_NETWORKS[@]}"; do
    if execute_cmd "docker network ls --format '{{.Name}}' | grep -q '^${network}$'"; then
        log "Removing network: $network"
        execute_cmd "docker network rm $network || true"
        success "Removed network: $network"
    else
        log "Network not found: $network"
    fi
done

# Step 5: Remove TMS images
log "Removing TMS images..."
TMS_IMAGES=("tms-api-prod-container:latest")

for image in "${TMS_IMAGES[@]}"; do
    if execute_cmd "docker images --format '{{.Repository}}:{{.Tag}}' | grep -q '^${image}$'"; then
        log "Removing image: $image"
        execute_cmd "docker rmi $image || true"
        success "Removed image: $image"
    else
        log "Image not found: $image"
    fi
done

# Step 6: Clean up dangling resources
log "Cleaning up dangling Docker resources..."
execute_cmd "docker system prune -f || true"

# Step 7: Show volumes (preserved)
log "TMS data volumes (preserved):"
execute_cmd "docker volume ls --format 'table {{.Name}}\t{{.Driver}}' | grep -E '(NAME|tms-.*-prod-volume)' || echo 'No TMS volumes found'"

# Step 8: Verification
echo ""
log "Verifying cleanup completion..."

remaining_containers=$(execute_cmd "docker ps -a --format '{{.Names}}' | grep -E '^tms-.*-prod-container$' | wc -l" || echo "0")
remaining_networks=$(execute_cmd "docker network ls --format '{{.Name}}' | grep -E '^tms' | wc -l" || echo "0")
remaining_images=$(execute_cmd "docker images --format '{{.Repository}}:{{.Tag}}' | grep -E '^tms-.*-prod-container' | wc -l" || echo "0")

echo ""
echo "📊 Cleanup Summary:"
echo "==================="
echo "Containers removed: $((4 - remaining_containers))/4"
echo "Networks removed: $((2 - remaining_networks))/2"  
echo "Images removed: $((1 - remaining_images))/1"

if [[ "$remaining_containers" -eq 0 && "$remaining_networks" -eq 0 && "$remaining_images" -eq 0 ]]; then
    echo ""
    success "🎉 Complete cleanup successful!"
    echo ""
    echo "✅ All TMS production containers removed"
    echo "✅ All TMS networks removed"
    echo "✅ All TMS images removed"
    echo "✅ Data volumes preserved"
    echo ""
    echo "You can now run the deployment script again:"
    echo "./deploy.sh"
else
    echo ""
    warning "⚠️  Partial cleanup completed"
    
    if [[ "$remaining_containers" -gt 0 ]]; then
        echo "Remaining containers:"
        execute_cmd "docker ps -a --format 'table {{.Names}}\t{{.Status}}' | grep -E '^tms-.*-prod-container' || true"
    fi
    
    if [[ "$remaining_networks" -gt 0 ]]; then
        echo "Remaining networks:"
        execute_cmd "docker network ls --format 'table {{.Name}}\t{{.Driver}}' | grep -E '^tms' || true"
    fi
    
    if [[ "$remaining_images" -gt 0 ]]; then
        echo "Remaining images:"
        execute_cmd "docker images --format 'table {{.Repository}}\t{{.Tag}}' | grep -E '^tms-.*-prod-container' || true"
    fi
    
    echo ""
    echo "You may need to manually remove remaining resources before deployment."
fi

echo ""
log "Cleanup script completed."
