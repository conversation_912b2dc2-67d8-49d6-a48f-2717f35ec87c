/**
 * Minio Service
 *
 * This service provides comprehensive methods for interacting with Minio object storage.
 * It handles connection management, file upload/download operations, bucket management,
 * and provides health checks for the Minio connection.
 */

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Minio from 'minio';
import { MinioConfig } from '../config/minio.config';
import { Readable } from 'stream';

/**
 * Interface for file upload options
 */
export interface FileUploadOptions {
  bucketName?: string;
  objectName: string;
  contentType?: string;
  metadata?: Record<string, string>;
}

/**
 * Interface for file upload result
 */
export interface FileUploadResult {
  bucketName: string;
  objectName: string;
  etag: string;
  size: number;
}

/**
 * Interface for file metadata
 */
export interface FileMetadata {
  bucketName: string;
  objectName: string;
  size: number;
  lastModified: Date;
  etag: string;
  contentType?: string;
  metadata?: Record<string, string>;
}

@Injectable()
export class MinioService implements OnModuleInit {
  private readonly logger = new Logger(MinioService.name);
  private minioClient!: Minio.Client;

  constructor(private readonly configService: ConfigService) {}

  onModuleInit(): void {
    const minioConfig = this.configService.get<MinioConfig>('minio');

    if (!minioConfig) {
      throw new Error('Minio configuration not found');
    }

    // Configure SSL options for self-signed certificates in production
    const clientOptions: any = {
      endPoint: minioConfig.endPoint,
      port: minioConfig.port,
      useSSL: minioConfig.useSSL,
      accessKey: minioConfig.accessKey,
      secretKey: minioConfig.secretKey,
    };

    // For production with self-signed certificates, disable SSL verification
    if (minioConfig.useSSL && process.env.NODE_ENV === 'production') {
      // Set up custom transport to ignore self-signed certificate errors
      const https = require('https');
      clientOptions.transportAgent = new https.Agent({
        rejectUnauthorized: false, // Allow self-signed certificates
      });
      this.logger.warn(
        'MinIO SSL certificate verification disabled for self-signed certificates in production',
      );
    }

    this.minioClient = new Minio.Client(clientOptions);

    this.logger.log(
      `Minio client initialized for ${minioConfig.endPoint}:${minioConfig.port}`,
    );
  }

  /**
   * Get the Minio client instance
   */
  getClient(): Minio.Client {
    if (!this.minioClient) {
      throw new Error('Minio client not initialized');
    }
    return this.minioClient;
  }

  /**
   * Check if the Minio connection is healthy
   */
  async isHealthy(): Promise<boolean> {
    try {
      if (!this.minioClient) {
        return false;
      }

      // Try to list buckets as a health check
      await this.minioClient.listBuckets();
      return true;
    } catch (error) {
      this.logger.error('Minio health check failed:', error);
      return false;
    }
  }

  /**
   * Get the current Minio connection status
   */
  async getStatus(): Promise<{ isConnected: boolean; message: string }> {
    const isConnected = await this.isHealthy();
    return {
      isConnected,
      message: isConnected
        ? 'Minio connection is healthy'
        : 'Minio connection is not established',
    };
  }

  /**
   * Check if a bucket exists
   */
  async bucketExists(bucketName: string): Promise<boolean> {
    try {
      return await this.minioClient.bucketExists(bucketName);
    } catch (error) {
      this.logger.error(
        `Error checking if bucket ${bucketName} exists:`,
        error,
      );
      return false;
    }
  }

  /**
   * Ensure a bucket exists, create it if it doesn't
   */
  async ensureBucket(bucketName: string): Promise<void> {
    try {
      const exists = await this.bucketExists(bucketName);
      if (!exists) {
        await this.minioClient.makeBucket(bucketName);
        this.logger.log(`Created bucket: ${bucketName}`);
      }
    } catch (error) {
      this.logger.error(`Error ensuring bucket ${bucketName} exists:`, error);
      throw error;
    }
  }

  /**
   * Upload a file to Minio
   */
  async uploadFile(
    fileBuffer: Buffer,
    options: FileUploadOptions,
  ): Promise<FileUploadResult> {
    try {
      const bucketName = options.bucketName || this.getDefaultBucket();

      // Ensure bucket exists
      await this.ensureBucket(bucketName);

      // Convert buffer to stream
      const stream = Readable.from(fileBuffer);

      // Upload the file
      const uploadInfo = await this.minioClient.putObject(
        bucketName,
        options.objectName,
        stream,
        fileBuffer.length,
        {
          'Content-Type': options.contentType || 'application/octet-stream',
          ...options.metadata,
        },
      );

      this.logger.log(
        `File uploaded successfully: ${bucketName}/${options.objectName}`,
      );

      return {
        bucketName,
        objectName: options.objectName,
        etag: uploadInfo.etag,
        size: fileBuffer.length,
      };
    } catch (error) {
      this.logger.error(`Error uploading file ${options.objectName}:`, error);
      throw error;
    }
  }

  /**
   * Download a file from Minio
   */
  async downloadFile(bucketName: string, objectName: string): Promise<Buffer> {
    try {
      const stream = await this.minioClient.getObject(bucketName, objectName);

      // Convert stream to buffer
      const chunks: Buffer[] = [];
      return new Promise((resolve, reject) => {
        stream.on('data', (chunk: Buffer) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error(
        `Error downloading file ${bucketName}/${objectName}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(
    bucketName: string,
    objectName: string,
  ): Promise<FileMetadata> {
    try {
      const stat = await this.minioClient.statObject(bucketName, objectName);

      return {
        bucketName,
        objectName,
        size: stat.size,
        lastModified: stat.lastModified,
        etag: stat.etag,
        contentType: stat.metaData?.['content-type'] as string | undefined,
        metadata: stat.metaData,
      };
    } catch (error) {
      this.logger.error(
        `Error getting metadata for ${bucketName}/${objectName}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Delete a file from Minio
   */
  async deleteFile(bucketName: string, objectName: string): Promise<void> {
    try {
      await this.minioClient.removeObject(bucketName, objectName);
      this.logger.log(`File deleted successfully: ${bucketName}/${objectName}`);
    } catch (error) {
      this.logger.error(
        `Error deleting file ${bucketName}/${objectName}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * List files in a bucket
   */
  async listFiles(bucketName: string, prefix?: string): Promise<string[]> {
    try {
      const objects: string[] = [];
      const stream = this.minioClient.listObjects(bucketName, prefix, true);

      return new Promise((resolve, reject) => {
        stream.on('data', (obj: Minio.BucketItem) =>
          objects.push(obj.name || ''),
        );
        stream.on('end', () => resolve(objects));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error(`Error listing files in bucket ${bucketName}:`, error);
      throw error;
    }
  }

  /**
   * Generate a pre-signed URL for accessing a file
   */
  async getPresignedUrl(
    bucketName: string,
    objectName: string,
    expirySeconds: number = 24 * 60 * 60, // Default 24 hours
  ): Promise<string> {
    try {
      const presignedUrl = await this.minioClient.presignedGetObject(
        bucketName,
        objectName,
        expirySeconds,
      );

      this.logger.debug(
        `Generated pre-signed URL for ${bucketName}/${objectName} with ${expirySeconds}s expiry`,
      );

      return presignedUrl;
    } catch (error) {
      this.logger.error(
        `Error generating pre-signed URL for ${bucketName}/${objectName}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove a bucket (only if it's empty)
   */
  async removeBucket(bucketName: string): Promise<void> {
    try {
      await this.minioClient.removeBucket(bucketName);
      this.logger.log(`Bucket removed successfully: ${bucketName}`);
    } catch (error) {
      this.logger.error(`Error removing bucket ${bucketName}:`, error);
      throw error;
    }
  }

  /**
   * List all buckets
   */
  async listBuckets(): Promise<string[]> {
    try {
      const buckets = await this.minioClient.listBuckets();
      return buckets.map((bucket) => bucket.name);
    } catch (error) {
      this.logger.error('Error listing buckets:', error);
      throw error;
    }
  }

  /**
   * Get the default bucket name from configuration
   */
  getDefaultBucket(): string {
    const minioConfig = this.configService.get<MinioConfig>('minio');
    if (!minioConfig) {
      throw new Error('Minio configuration not found');
    }
    return minioConfig.defaultBucket;
  }
}
