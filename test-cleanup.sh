#!/bin/bash

# Test script to verify cleanup functionality
# This script tests the cleanup logic locally before deployment

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🧪 Testing TMS Container Cleanup Logic"
echo "======================================"
echo ""

# Test 1: Check if cleanup functions are properly defined
log "Test 1: Verifying cleanup functions in deployment script..."

if grep -q "cleanup_containers()" redeploy-production.sh; then
    success "cleanup_containers() function found"
else
    error "cleanup_containers() function missing"
    exit 1
fi

if grep -q "cleanup_networks()" redeploy-production.sh; then
    success "cleanup_networks() function found"
else
    error "cleanup_networks() function missing"
    exit 1
fi

if grep -q "cleanup_images()" redeploy-production.sh; then
    success "cleanup_images() function found"
else
    error "cleanup_images() function missing"
    exit 1
fi

# Test 2: Check container name patterns
log "Test 2: Verifying container name patterns..."

expected_containers=(
    "tms-api-prod-container"
    "tms-postgres-prod-container"
    "tms-minio-prod-container"
    "tms-reverse-proxy-prod-container"
)

for container in "${expected_containers[@]}"; do
    if grep -q "$container" redeploy-production.sh; then
        success "Container pattern found: $container"
    else
        error "Container pattern missing: $container"
        exit 1
    fi
done

# Test 3: Check if docker-compose.prod.yml has matching container names
log "Test 3: Verifying container names match docker-compose.prod.yml..."

if [[ -f "docker-compose.prod.yml" ]]; then
    for container in "${expected_containers[@]}"; do
        if grep -q "container_name: $container" docker-compose.prod.yml; then
            success "Container name matches in compose file: $container"
        else
            error "Container name mismatch in compose file: $container"
            exit 1
        fi
    done
else
    error "docker-compose.prod.yml not found"
    exit 1
fi

# Test 4: Check cleanup verification logic
log "Test 4: Verifying cleanup verification logic..."

if grep -q "remaining_containers.*grep.*tms.*prod-container" redeploy-production.sh; then
    success "Container verification logic found"
else
    error "Container verification logic missing"
    exit 1
fi

# Test 5: Check error handling
log "Test 5: Verifying error handling..."

if grep -q "|| true" redeploy-production.sh; then
    success "Error handling with '|| true' found"
else
    error "Error handling missing"
    exit 1
fi

# Test 6: Check if cleanup script exists and is executable
log "Test 6: Verifying standalone cleanup script..."

if [[ -f "cleanup-production.sh" && -x "cleanup-production.sh" ]]; then
    success "Standalone cleanup script exists and is executable"
else
    error "Standalone cleanup script missing or not executable"
    exit 1
fi

# Test 7: Simulate container name conflict detection
log "Test 7: Testing container conflict detection logic..."

# Create a test function that simulates the container check
test_container_check() {
    local container_name="$1"
    # Simulate docker ps -a output
    echo "test-container-1"
    echo "$container_name"
    echo "test-container-2"
}

# Test the grep pattern used in the script
if test_container_check "tms-postgres-prod-container" | grep -q '^tms-postgres-prod-container$'; then
    success "Container detection pattern works correctly"
else
    error "Container detection pattern fails"
    exit 1
fi

# Test 8: Check deployment script structure
log "Test 8: Verifying deployment script structure..."

required_sections=(
    "Comprehensive cleanup of existing deployment"
    "cleanup_containers"
    "cleanup_networks"
    "cleanup_images"
    "Verifying cleanup completion"
)

for section in "${required_sections[@]}"; do
    if grep -q "$section" redeploy-production.sh; then
        success "Required section found: $section"
    else
        error "Required section missing: $section"
        exit 1
    fi
done

# Test 9: Check if volumes are preserved
log "Test 9: Verifying data volume preservation..."

if grep -q "Volume will be preserved to avoid data loss" redeploy-production.sh; then
    success "Volume preservation logic found"
else
    error "Volume preservation logic missing"
    exit 1
fi

# Test 10: Check remote execution capability
log "Test 10: Verifying remote execution functions..."

if grep -q "execute_cmd" redeploy-production.sh && grep -q "check_environment" redeploy-production.sh; then
    success "Remote execution capability found"
else
    error "Remote execution capability missing"
    exit 1
fi

echo ""
echo "🎉 All cleanup tests passed!"
echo ""
echo "Summary of improvements:"
echo "======================="
echo "✅ Comprehensive container cleanup with individual removal"
echo "✅ Network cleanup to prevent conflicts"
echo "✅ Image cleanup with verification"
echo "✅ Volume preservation to protect data"
echo "✅ Detailed logging and error handling"
echo "✅ Verification of cleanup completion"
echo "✅ Standalone cleanup script for troubleshooting"
echo "✅ Remote execution capability"
echo ""
echo "The deployment script should now handle container conflicts properly."
echo ""
echo "To test the deployment:"
echo "1. Run: ./deploy.sh"
echo ""
echo "If you encounter issues:"
echo "1. Run: ./cleanup-production.sh"
echo "2. Then: ./deploy.sh"
