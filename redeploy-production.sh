#!/bin/bash

# Production Deployment Script for TMS REST API
# Comprehensive deployment with cleanup, verification, and rollback capabilities
# Updated for new authentication credentials: tms-server:dr2025du

set -e  # Exit on any error

# Configuration
TARGET_SERVER="williamdu@***********"
API_URL="https://***********"
NEW_AUTH_USERNAME="tms-server"
NEW_AUTH_PASSWORD="dr2025du"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if we're running locally or on the server
check_environment() {
    if [[ $(hostname) == *"***********"* ]] || [[ $(hostname -I 2>/dev/null | grep -q "***********") ]]; then
        echo "server"
    else
        echo "local"
    fi
}

# Function to execute commands locally or remotely
execute_cmd() {
    local cmd="$1"
    local env=$(check_environment)

    if [[ "$env" == "server" ]]; then
        # Running on server
        eval "$cmd"
    else
        # Running locally, execute on remote server
        ssh "$TARGET_SERVER" "$cmd"
    fi
}

# Function to copy files to server if running locally
copy_to_server() {
    local env=$(check_environment)

    if [[ "$env" == "local" ]]; then
        log "Copying project files to server..."
        rsync -avz --exclude 'node_modules' --exclude '.git' --exclude 'logs' \
              ./ "$TARGET_SERVER:~/tms-api/"
        success "Files copied to server"
    else
        log "Running on server, no file copy needed"
    fi
}

echo "🚀 TMS REST API Production Deployment Script"
echo "=============================================="
log "Target: $API_URL"
log "New Auth: $NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD"
echo ""

# Step 1: Pre-deployment checks
log "Step 1: Pre-deployment verification..."

# Check if secrets files exist and have correct credentials
if [[ ! -f "secrets/auth_username.txt" ]] || [[ ! -f "secrets/auth_password.txt" ]]; then
    error "Authentication secret files not found!"
    exit 1
fi

current_username=$(cat secrets/auth_username.txt)
current_password=$(cat secrets/auth_password.txt)

if [[ "$current_username" != "$NEW_AUTH_USERNAME" ]] || [[ "$current_password" != "$NEW_AUTH_PASSWORD" ]]; then
    error "Secret files don't match expected credentials!"
    echo "Expected: $NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD"
    echo "Found: $current_username:$current_password"
    exit 1
fi

success "Authentication credentials verified"

# Step 2: Copy files to server (if running locally)
copy_to_server

# Step 3: Server-side deployment
log "Step 2: Starting server-side deployment..."

execute_cmd "cd ~/tms-api"

# Step 4: Comprehensive cleanup of existing deployment
log "Step 3: Performing comprehensive cleanup of existing deployment..."

# Function to safely remove containers
cleanup_containers() {
    local container_names=(
        "tms-api-prod-container"
        "tms-postgres-prod-container"
        "tms-minio-prod-container"
        "tms-reverse-proxy-prod-container"
    )

    log "Stopping and removing TMS production containers..."

    for container_name in "${container_names[@]}"; do
        # Check if container exists (running or stopped)
        if execute_cmd "docker ps -a --format '{{.Names}}' | grep -q '^${container_name}$'"; then
            log "Found existing container: $container_name"

            # Stop container if running
            if execute_cmd "docker ps --format '{{.Names}}' | grep -q '^${container_name}$'"; then
                log "Stopping running container: $container_name"
                execute_cmd "docker stop $container_name || true"
            fi

            # Remove container
            log "Removing container: $container_name"
            execute_cmd "docker rm -f $container_name || true"

            # Verify removal
            if execute_cmd "docker ps -a --format '{{.Names}}' | grep -q '^${container_name}$'"; then
                error "Failed to remove container: $container_name"
                exit 1
            else
                success "Successfully removed container: $container_name"
            fi
        else
            log "Container not found (already clean): $container_name"
        fi
    done
}

# Function to cleanup networks
cleanup_networks() {
    log "Cleaning up TMS networks..."
    local networks=("tms-internal" "tms-api_tms-internal")

    for network in "${networks[@]}"; do
        if execute_cmd "docker network ls --format '{{.Name}}' | grep -q '^${network}$'"; then
            log "Removing network: $network"
            execute_cmd "docker network rm $network || true"
        fi
    done
}

# Function to cleanup volumes (with confirmation)
cleanup_volumes() {
    log "Checking for TMS volumes..."
    local volumes=("tms-postgres-prod-volume" "tms-minio-prod-volume")

    for volume in "${volumes[@]}"; do
        if execute_cmd "docker volume ls --format '{{.Name}}' | grep -q '^${volume}$'"; then
            warning "Found existing volume: $volume (contains data)"
            log "Volume will be preserved to avoid data loss"
            # Note: We don't remove volumes to preserve data
        fi
    done
}

# Function to cleanup images
cleanup_images() {
    log "Cleaning up TMS images..."

    # Remove specific TMS images
    local images=("tms-api-prod-container:latest")

    for image in "${images[@]}"; do
        if execute_cmd "docker images --format '{{.Repository}}:{{.Tag}}' | grep -q '^${image}$'"; then
            log "Removing image: $image"
            execute_cmd "docker rmi $image || true"
        fi
    done

    # Clean up dangling images
    log "Removing dangling images..."
    execute_cmd "docker image prune -f || true"
}

# Execute cleanup steps
log "Starting comprehensive cleanup process..."

# Step 1: Try docker-compose down first (graceful shutdown)
log "Attempting graceful shutdown with docker-compose..."
execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml down --remove-orphans --volumes || true"

# Step 2: Force remove individual containers
cleanup_containers

# Step 3: Clean up networks
cleanup_networks

# Step 4: Clean up images
cleanup_images

# Step 5: Check volumes (but preserve data)
cleanup_volumes

# Step 6: General Docker cleanup
log "Performing general Docker system cleanup..."
execute_cmd "docker system prune -f || true"

# Step 7: Verify cleanup
log "Verifying cleanup completion..."
remaining_containers=$(execute_cmd "docker ps -a --format '{{.Names}}' | grep -E '^tms-.*-prod-container$' | wc -l" || echo "0")

if [[ "$remaining_containers" -gt 0 ]]; then
    warning "Some TMS containers may still exist:"
    execute_cmd "docker ps -a --format 'table {{.Names}}\t{{.Status}}' | grep -E '^tms-.*-prod-container' || true"
else
    success "All TMS production containers successfully removed"
fi

success "Comprehensive cleanup completed"

# Step 5: Build production image
log "Step 4: Building production Docker image..."
execute_cmd "cd ~/tms-api && docker build -t tms-api-prod-container:latest --target production ."

success "Production image built"

# Step 6: Start production services
log "Step 5: Starting production services..."
execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml up -d"

success "Production services started"

# Step 7: Wait for services to be ready
log "Step 6: Waiting for services to initialize..."
sleep 45

# Step 8: Health checks
log "Step 7: Performing comprehensive health checks..."

# Basic connectivity check
log "Checking basic HTTPS connectivity..."
HEALTH_CORRELATION_ID="$(uuidgen 2>/dev/null || python3 -c 'import uuid; print(uuid.uuid4())' 2>/dev/null || echo 'deploy-$(date +%s)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(date +%N)')"
if execute_cmd "curl -k -f --connect-timeout 10 --max-time 15 -H 'X-Correlation-ID: $HEALTH_CORRELATION_ID' $API_URL/health > /dev/null 2>&1"; then
    success "Basic connectivity check passed"
else
    error "Basic connectivity check failed"
    execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml logs api"
    exit 1
fi

# Authentication check with new credentials
log "Testing authentication with new credentials..."
AUTH_HEADER=$(echo -n "$NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD" | base64)
AUTH_CORRELATION_ID="$(uuidgen 2>/dev/null || python3 -c 'import uuid; print(uuid.uuid4())' 2>/dev/null || echo 'auth-$(date +%s)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(date +%N)')"
if execute_cmd "curl -k -f --connect-timeout 10 --max-time 15 -H 'Authorization: Basic $AUTH_HEADER' -H 'X-Correlation-ID: $AUTH_CORRELATION_ID' $API_URL/protected > /dev/null 2>&1"; then
    success "Authentication check passed with new credentials"
else
    error "Authentication check failed with new credentials"
    execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml logs api"
    exit 1
fi

# Container health check
log "Checking container health status..."
execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml ps"

# Step 9: Final verification
log "Step 8: Final deployment verification..."

# Test all critical endpoints
log "Testing critical API endpoints..."

# Test health endpoint (no auth required, but correlation ID required)
FINAL_HEALTH_CORRELATION_ID="$(uuidgen 2>/dev/null || python3 -c 'import uuid; print(uuid.uuid4())' 2>/dev/null || echo 'final-$(date +%s)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(date +%N)')"
if execute_cmd "curl -k -s --connect-timeout 10 --max-time 15 -H 'X-Correlation-ID: $FINAL_HEALTH_CORRELATION_ID' $API_URL/health | grep -q 'status.*ok'"; then
    success "Health endpoint responding correctly"
else
    warning "Health endpoint response unexpected"
fi

# Test protected endpoint (auth required)
PROTECTED_CORRELATION_ID="$(uuidgen 2>/dev/null || python3 -c 'import uuid; print(uuid.uuid4())' 2>/dev/null || echo 'protected-$(date +%s)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(date +%N)')"
if execute_cmd "curl -k -s --connect-timeout 10 --max-time 15 -H 'Authorization: Basic $AUTH_HEADER' -H 'X-Correlation-ID: $PROTECTED_CORRELATION_ID' $API_URL/protected | grep -q 'This is a protected endpoint that requires Basic Auth'"; then
    success "Protected endpoint responding correctly"
else
    warning "Protected endpoint response unexpected"
fi

# Test quiz endpoint structure (auth required)
QUIZ_CORRELATION_ID="$(uuidgen 2>/dev/null || python3 -c 'import uuid; print(uuid.uuid4())' 2>/dev/null || echo 'quiz-$(date +%s)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(date +%N)')"
if execute_cmd "curl -k -s --connect-timeout 10 --max-time 15 -H 'Authorization: Basic $AUTH_HEADER' -H 'X-Correlation-ID: $QUIZ_CORRELATION_ID' '$API_URL/quiz/f2f/paperless-marking-worked-solutions?year=2025&term=1&week=1&weekType=normal' | grep -q 'quizzes'"; then
    success "Quiz endpoint responding correctly"
else
    warning "Quiz endpoint response unexpected (may be empty, which is normal)"
fi

success "Deployment verification completed"

# Step 10: Display deployment summary
echo ""
echo "🎉 Production Deployment Completed Successfully!"
echo "=============================================="
log "API URL: $API_URL"
log "Health Check: $API_URL/health"
log "API Documentation: $API_URL/api/docs"
log "Authentication: $NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD"
echo ""

# Display running containers
log "Production containers status:"
execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml ps"

echo ""
success "Deployment completed successfully!"
echo ""
echo "🔐 Test the deployment with:"
echo "# Health check (no auth required):"
echo "curl -k -H 'X-Correlation-ID: \$(uuidgen)' '$API_URL/health'"
echo ""
echo "# Protected endpoint (auth required):"
echo "curl -k -u '$NEW_AUTH_USERNAME:$NEW_AUTH_PASSWORD' -H 'X-Correlation-ID: \$(uuidgen)' '$API_URL/protected'"
echo ""
echo "📚 Access API documentation at: $API_URL/api/docs"
echo ""

# Rollback function (for manual use if needed)
rollback() {
    error "Initiating rollback procedure..."
    execute_cmd "cd ~/tms-api && docker-compose -f docker-compose.prod.yml down"
    error "Services stopped. Manual intervention required."
    exit 1
}

# Export rollback function for manual use
export -f rollback
